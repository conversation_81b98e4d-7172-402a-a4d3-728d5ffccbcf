import '../models/chat_models.dart';
import '../models/tool_models.dart';
import 'llm_error.dart';

/// Response from a chat provider
abstract class ChatResponse {
  /// Get the text content of the response
  String? get text;

  /// Get tool calls from the response
  List<ToolCall>? get toolCalls;

  /// Get thinking/reasoning content (for providers that support it)
  String? get thinking => null;

  /// Get usage information if available
  UsageInfo? get usage => null;
}

/// Usage information for API calls
class UsageInfo {
  final int? promptTokens;
  final int? completionTokens;
  final int? totalTokens;

  const UsageInfo({this.promptTokens, this.completionTokens, this.totalTokens});

  /// Add usage information from another UsageInfo instance
  UsageInfo operator +(UsageInfo? other) {
    if (other == null) return this;
    return UsageInfo(
      promptTokens: (promptTokens ?? 0) + (other.promptTokens ?? 0),
      completionTokens: (completionTokens ?? 0) + (other.completionTokens ?? 0),
      totalTokens: (totalTokens ?? 0) + (other.totalTokens ?? 0),
    );
  }

  /// Create a copy with optional parameter overrides
  UsageInfo copyWith({
    int? promptTokens,
    int? completionTokens,
    int? totalTokens,
  }) => UsageInfo(
    promptTokens: promptTokens ?? this.promptTokens,
    completionTokens: completionTokens ?? this.completionTokens,
    totalTokens: totalTokens ?? this.totalTokens,
  );

  Map<String, dynamic> toJson() => {
    if (promptTokens != null) 'prompt_tokens': promptTokens,
    if (completionTokens != null) 'completion_tokens': completionTokens,
    if (totalTokens != null) 'total_tokens': totalTokens,
  };

  factory UsageInfo.fromJson(Map<String, dynamic> json) => UsageInfo(
    promptTokens: json['prompt_tokens'] as int?,
    completionTokens: json['completion_tokens'] as int?,
    totalTokens: json['total_tokens'] as int?,
  );

  @override
  String toString() =>
      'UsageInfo(prompt: $promptTokens, completion: $completionTokens, total: $totalTokens)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UsageInfo &&
          runtimeType == other.runtimeType &&
          promptTokens == other.promptTokens &&
          completionTokens == other.completionTokens &&
          totalTokens == other.totalTokens;

  @override
  int get hashCode => Object.hash(promptTokens, completionTokens, totalTokens);
}

/// Configuration options for chat requests
class ChatOptions {
  /// Model to use for this specific request (overrides provider default)
  final String? model;

  /// Maximum tokens to generate
  final int? maxTokens;

  /// Temperature for response randomness (0.0-1.0)
  final double? temperature;

  /// Stop sequences to halt generation
  final List<String>? stop;

  /// Seed for deterministic generation
  final int? seed;

  /// Top-p (nucleus) sampling parameter
  final double? topP;

  /// Top-k sampling parameter
  final int? topK;

  /// Provider-specific parameters that don't fit in the common interface
  final Map<String, dynamic>? extraParams;

  const ChatOptions({
    this.model,
    this.maxTokens,
    this.temperature,
    this.stop,
    this.seed,
    this.topP,
    this.topK,
    this.extraParams,
  });

  /// Create a copy with optional parameter overrides
  ChatOptions copyWith({
    String? model,
    int? maxTokens,
    double? temperature,
    List<String>? stop,
    int? seed,
    double? topP,
    int? topK,
    Map<String, dynamic>? extraParams,
  }) => ChatOptions(
    model: model ?? this.model,
    maxTokens: maxTokens ?? this.maxTokens,
    temperature: temperature ?? this.temperature,
    stop: stop ?? this.stop,
    seed: seed ?? this.seed,
    topP: topP ?? this.topP,
    topK: topK ?? this.topK,
    extraParams: extraParams ?? this.extraParams,
  );

  Map<String, dynamic> toJson() => {
    if (model != null) 'model': model,
    if (maxTokens != null) 'max_tokens': maxTokens,
    if (temperature != null) 'temperature': temperature,
    if (stop != null) 'stop': stop,
    if (seed != null) 'seed': seed,
    if (topP != null) 'top_p': topP,
    if (topK != null) 'top_k': topK,
    if (extraParams != null) ...extraParams!,
  };

  @override
  String toString() =>
      'ChatOptions(model: $model, maxTokens: $maxTokens, temperature: $temperature)';
}

/// Trait for providers that support chat-style interactions.
abstract class ChatProvider {
  /// Sends a chat request to the provider with a sequence of messages.
  ///
  /// [messages] - The conversation history as a list of chat messages
  /// [options] - Optional configuration for this specific request
  ///
  /// Returns the provider's response or throws an LLMError
  Future<ChatResponse> chat(
    List<ChatMessage> messages, {
    ChatOptions? options,
  }) async {
    return chatWithTools(messages, null, options: options);
  }

  /// Sends a chat request to the provider with a sequence of messages and tools.
  ///
  /// [messages] - The conversation history as a list of chat messages
  /// [tools] - Optional list of tools to use in the chat
  /// [options] - Optional configuration for this specific request
  ///
  /// Returns the provider's response or throws an LLMError
  Future<ChatResponse> chatWithTools(
    List<ChatMessage> messages,
    List<Tool>? tools, {
    ChatOptions? options,
  });

  /// Get current memory contents if provider supports memory
  Future<List<ChatMessage>?> memoryContents() async => null;

  /// Summarizes a conversation history into a concise 2-3 sentence summary
  ///
  /// [messages] - The conversation messages to summarize
  ///
  /// Returns a string containing the summary or throws an LLMError
  Future<String> summarizeHistory(List<ChatMessage> messages) async {
    final prompt =
        'Summarize in 2-3 sentences:\n${messages.map((m) => '${m.role.name}: ${m.content}').join('\n')}';
    final request = [ChatMessage.user(prompt)];
    final response = await chat(request);
    final text = response.text;
    if (text == null) {
      throw const GenericError('no text in summary response');
    }
    return text;
  }
}

/// Stream event for streaming chat responses
sealed class ChatStreamEvent {
  const ChatStreamEvent();
}

/// Text delta event
class TextDeltaEvent extends ChatStreamEvent {
  final String delta;

  const TextDeltaEvent(this.delta);
}

/// Tool call delta event
class ToolCallDeltaEvent extends ChatStreamEvent {
  final ToolCall toolCall;

  const ToolCallDeltaEvent(this.toolCall);
}

/// Completion event
class CompletionEvent extends ChatStreamEvent {
  final ChatResponse response;

  const CompletionEvent(this.response);
}

/// Thinking/reasoning delta event for reasoning models
class ThinkingDeltaEvent extends ChatStreamEvent {
  final String delta;

  const ThinkingDeltaEvent(this.delta);
}

/// Error event
class ErrorEvent extends ChatStreamEvent {
  final LLMError error;

  const ErrorEvent(this.error);
}

/// Trait for providers that support streaming chat interactions
abstract class StreamingChatProvider extends ChatProvider {
  /// Sends a streaming chat request to the provider
  ///
  /// [messages] - The conversation history as a list of chat messages
  /// [tools] - Optional list of tools to use in the chat
  /// [options] - Optional configuration for this specific request
  ///
  /// Returns a stream of chat events
  Stream<ChatStreamEvent> chatStream(
    List<ChatMessage> messages, {
    List<Tool>? tools,
    ChatOptions? options,
  });

  /// Default implementation: builds a complete response by consuming the stream
  ///
  /// Concrete providers only need to implement [chatStream] - this method
  /// provides the synchronous interface by collecting all stream events.
  @override
  Future<ChatResponse> chatWithTools(
    List<ChatMessage> messages,
    List<Tool>? tools, {
    ChatOptions? options,
  }) async {
    final stream = chatStream(messages, tools: tools, options: options);
    ChatResponse? finalResponse;
    LLMError? error;
    Object? streamError;

    try {
      await for (final event in stream) {
        if (event is CompletionEvent) {
          // Final event contains the complete response
          finalResponse = event.response;
        } else if (event is ErrorEvent) {
          error = event.error;
          break; // Stop on error
        }
        // TextDeltaEvent/ToolCallDeltaEvent/ThinkingDeltaEvent are ignored here
        // since we only care about the final complete result
      }
    } catch (e) {
      // Capture stream exceptions
      streamError = e;
    }

    if (error != null) throw error;
    if (streamError != null) {
      if (streamError is LLMError) {
        throw streamError;
      } else {
        throw GenericError('Stream error: $streamError');
      }
    }

    if (finalResponse != null) {
      return finalResponse;
    }

    // Stream ended without completion or error event
    throw const GenericError(
      'Stream finished without a completion or error event',
    );
  }
}

/// Completion request for text completion providers
class CompletionRequest {
  final String prompt;
  final int? maxTokens;
  final double? temperature;
  final double? topP;
  final int? topK;
  final List<String>? stop;

  const CompletionRequest({
    required this.prompt,
    this.maxTokens,
    this.temperature,
    this.topP,
    this.topK,
    this.stop,
  });

  Map<String, dynamic> toJson() => {
    'prompt': prompt,
    if (maxTokens != null) 'max_tokens': maxTokens,
    if (temperature != null) 'temperature': temperature,
    if (topP != null) 'top_p': topP,
    if (topK != null) 'top_k': topK,
    if (stop != null) 'stop': stop,
  };
}

/// Completion response from text completion providers
class CompletionResponse {
  final String text;
  final UsageInfo? usage;

  const CompletionResponse({required this.text, this.usage});

  @override
  String toString() => text;
}

/// Trait for providers that support text completion
abstract class CompletionProvider {
  /// Sends a completion request to generate text
  ///
  /// [request] - The completion request parameters
  ///
  /// Returns the generated completion text or throws an LLMError
  Future<CompletionResponse> complete(CompletionRequest request);
}

/// Trait for providers that support vector embeddings
abstract class EmbeddingProvider {
  /// Generate embeddings for the given input texts
  ///
  /// [input] - List of strings to generate embeddings for
  ///
  /// Returns a list of embedding vectors or throws an LLMError
  Future<List<List<double>>> embed(List<String> input);
}

/// Trait for providers that support speech-to-text conversion
abstract class SpeechToTextProvider {
  /// Transcribe audio data to text
  ///
  /// [audio] - Raw audio data as bytes
  ///
  /// Returns transcribed text or throws an LLMError
  Future<String> transcribe(List<int> audio);

  /// Transcribe audio file to text
  ///
  /// [filePath] - Path to the audio file
  ///
  /// Returns transcribed text or throws an LLMError
  Future<String> transcribeFile(String filePath);
}

/// Trait for providers that support text-to-speech conversion
abstract class TextToSpeechProvider {
  /// Convert text to speech audio
  ///
  /// [text] - Text to convert to speech
  ///
  /// Returns audio data as bytes or throws an LLMError
  Future<List<int>> speech(String text);
}

/// Trait for providers that support model listing
abstract class ModelProvider {
  /// Get available models from the provider
  ///
  /// Returns a list of available models or throws an LLMError
  Future<List<AIModel>> models();
}

/// Marker interface for LLM providers
///
/// This is a lightweight marker interface that doesn't force implementations
/// to support all capabilities. Providers should implement only the specific
/// interfaces they actually support (ChatProvider, EmbeddingProvider, etc.)
///
/// This follows the Interface Segregation Principle - clients should not be
/// forced to depend on interfaces they don't use.
abstract class LLMProvider {
  /// Get available tools for this provider
  List<Tool>? get tools => null;
}
