import 'dart:convert';
import 'dart:async';
import 'package:dio/dio.dart';
import 'package:logging/logging.dart';

import '../core/chat_provider.dart';
import '../core/llm_error.dart';
import '../models/chat_models.dart';
import '../models/tool_models.dart';

/// Search source configuration for search parameters
class SearchSource {
  /// Type of source: "web" or "news"
  final String sourceType;

  /// List of websites to exclude from this source
  final List<String>? excludedWebsites;

  const SearchSource({required this.sourceType, this.excludedWebsites});

  Map<String, dynamic> toJson() => {
    'type': sourceType,
    if (excludedWebsites != null) 'excluded_websites': excludedWebsites,
  };
}

/// Search parameters for LLM providers that support search functionality
class SearchParameters {
  /// Search mode (e.g., "auto")
  final String? mode;

  /// List of search sources with exclusions
  final List<SearchSource>? sources;

  /// Maximum number of search results to return
  final int? maxSearchResults;

  /// Start date for search results (format: "YYYY-MM-DD")
  final String? fromDate;

  /// End date for search results (format: "YYYY-MM-DD")
  final String? toDate;

  const SearchParameters({
    this.mode,
    this.sources,
    this.maxSearchResults,
    this.fromDate,
    this.toDate,
  });

  Map<String, dynamic> toJson() => {
    if (mode != null) 'mode': mode,
    if (sources != null) 'sources': sources!.map((s) => s.toJson()).toList(),
    if (maxSearchResults != null) 'max_search_results': maxSearchResults,
    if (fromDate != null) 'from_date': fromDate,
    if (toDate != null) 'to_date': toDate,
  };
}

/// xAI provider configuration
class XAIConfig {
  final String apiKey;
  final String baseUrl;
  final String model;
  final int? maxTokens;
  final double? temperature;
  final String? systemPrompt;
  final Duration? timeout;
  final bool stream;
  final double? topP;
  final int? topK;
  final List<Tool>? tools;
  final ToolChoice? toolChoice;
  final StructuredOutputFormat? jsonSchema;
  final String? embeddingEncodingFormat;
  final int? embeddingDimensions;
  final SearchParameters? searchParameters;

  const XAIConfig({
    required this.apiKey,
    this.baseUrl = 'https://api.x.ai/v1/',
    this.model = 'grok-2-latest',
    this.maxTokens,
    this.temperature,
    this.systemPrompt,
    this.timeout,
    this.stream = false,
    this.topP,
    this.topK,
    this.tools,
    this.toolChoice,
    this.jsonSchema,
    this.embeddingEncodingFormat,
    this.embeddingDimensions,
    this.searchParameters,
  });

  XAIConfig copyWith({
    String? apiKey,
    String? baseUrl,
    String? model,
    int? maxTokens,
    double? temperature,
    String? systemPrompt,
    Duration? timeout,
    bool? stream,
    double? topP,
    int? topK,
    List<Tool>? tools,
    ToolChoice? toolChoice,
    StructuredOutputFormat? jsonSchema,
    String? embeddingEncodingFormat,
    int? embeddingDimensions,
    SearchParameters? searchParameters,
  }) => XAIConfig(
    apiKey: apiKey ?? this.apiKey,
    baseUrl: baseUrl ?? this.baseUrl,
    model: model ?? this.model,
    maxTokens: maxTokens ?? this.maxTokens,
    temperature: temperature ?? this.temperature,
    systemPrompt: systemPrompt ?? this.systemPrompt,
    timeout: timeout ?? this.timeout,
    stream: stream ?? this.stream,
    topP: topP ?? this.topP,
    topK: topK ?? this.topK,
    tools: tools ?? this.tools,
    toolChoice: toolChoice ?? this.toolChoice,
    jsonSchema: jsonSchema ?? this.jsonSchema,
    embeddingEncodingFormat:
        embeddingEncodingFormat ?? this.embeddingEncodingFormat,
    embeddingDimensions: embeddingDimensions ?? this.embeddingDimensions,
    searchParameters: searchParameters ?? this.searchParameters,
  );
}

/// xAI chat response implementation
class XAIChatResponse implements ChatResponse {
  final Map<String, dynamic> _rawResponse;

  XAIChatResponse(this._rawResponse);

  @override
  String? get text {
    final choices = _rawResponse['choices'] as List?;
    if (choices == null || choices.isEmpty) return null;

    final message = choices.first['message'] as Map<String, dynamic>?;
    return message?['content'] as String?;
  }

  @override
  List<ToolCall>? get toolCalls {
    // XAI doesn't support tool calls yet
    return null;
  }

  @override
  UsageInfo? get usage {
    final usageData = _rawResponse['usage'] as Map<String, dynamic>?;
    if (usageData == null) return null;

    return UsageInfo.fromJson(usageData);
  }

  @override
  String? get thinking => null; // XAI doesn't support thinking/reasoning content

  @override
  String toString() {
    final textContent = text;
    final calls = toolCalls;

    if (textContent != null && calls != null) {
      return '${calls.map((c) => c.toString()).join('\n')}\n$textContent';
    } else if (textContent != null) {
      return textContent;
    } else if (calls != null) {
      return calls.map((c) => c.toString()).join('\n');
    } else {
      return '';
    }
  }
}

/// Embedding data from xAI API
class XAIEmbeddingData {
  final List<double> embedding;

  XAIEmbeddingData(this.embedding);

  factory XAIEmbeddingData.fromJson(Map<String, dynamic> json) {
    final embeddingList = json['embedding'] as List;
    return XAIEmbeddingData(
      embeddingList.map((e) => (e as num).toDouble()).toList(),
    );
  }
}

/// Embedding response from xAI API
class XAIEmbeddingResponse {
  final List<XAIEmbeddingData> data;

  XAIEmbeddingResponse(this.data);

  factory XAIEmbeddingResponse.fromJson(Map<String, dynamic> json) {
    final dataList = json['data'] as List;
    return XAIEmbeddingResponse(
      dataList
          .map(
            (item) => XAIEmbeddingData.fromJson(item as Map<String, dynamic>),
          )
          .toList(),
    );
  }
}

/// xAI provider implementation
class XAIProvider
    implements StreamingChatProvider, CompletionProvider, LLMProvider {
  final XAIConfig config;
  final Dio _dio;
  final Logger _logger = Logger('XAIProvider');

  XAIProvider(this.config) : _dio = _createDio(config);

  // Note: chat method is provided by ChatProvider base class

  @override
  Future<List<ChatMessage>?> memoryContents() async => null;

  @override
  Future<String> summarizeHistory(List<ChatMessage> messages) async {
    final prompt =
        'Summarize in 2-3 sentences:\n${messages.map((m) => '${m.role.name}: ${m.content}').join('\n')}';
    final request = [ChatMessage.user(prompt)];
    final response = await chat(request);
    final text = response.text;
    if (text == null) {
      throw const GenericError('no text in summary response');
    }
    return text;
  }

  static Dio _createDio(XAIConfig config) {
    final dio = Dio(
      BaseOptions(
        baseUrl: config.baseUrl,
        connectTimeout: config.timeout ?? const Duration(seconds: 30),
        receiveTimeout: config.timeout ?? const Duration(seconds: 30),
        headers: {
          'Authorization': 'Bearer ${config.apiKey}',
          'Content-Type': 'application/json',
        },
      ),
    );

    return dio;
  }

  // Note: chatWithTools is provided by StreamingChatProvider's default implementation
  // which consumes the chatStream. We only need to implement chatStream.

  @override
  Stream<ChatStreamEvent> chatStream(
    List<ChatMessage> messages, {
    List<Tool>? tools,
    ChatOptions? options,
  }) async* {
    if (config.apiKey.isEmpty) {
      yield ErrorEvent(const AuthError('Missing xAI API key'));
      return;
    }

    try {
      final requestBody = _buildRequestBody(messages, tools, true, options);

      final response = await _dio.post(
        'chat/completions',
        data: requestBody,
        options: Options(responseType: ResponseType.stream),
      );

      if (response.statusCode != 200) {
        yield ErrorEvent(
          ProviderError('xAI API returned status ${response.statusCode}'),
        );
        return;
      }

      final stream = response.data as ResponseBody;
      await for (final chunk in stream.stream.map(utf8.decode)) {
        final lines = chunk.split('\n');
        for (final line in lines) {
          if (line.startsWith('data: ')) {
            final data = line.substring(6).trim();
            if (data == '[DONE]') {
              return;
            }

            try {
              final json = jsonDecode(data) as Map<String, dynamic>;
              final event = _parseStreamEvent(json);
              if (event != null) {
                yield event;
              }
            } catch (e) {
              // Skip malformed JSON chunks
              continue;
            }
          }
        }
      }
    } on DioException catch (e) {
      yield ErrorEvent(_handleDioError(e));
    } catch (e) {
      yield ErrorEvent(GenericError('Unexpected error: $e'));
    }
  }

  Map<String, dynamic> _buildRequestBody(
    List<ChatMessage> messages,
    List<Tool>? tools,
    bool stream,
    ChatOptions? options,
  ) {
    final apiMessages = <Map<String, dynamic>>[];

    // Add system message if configured
    if (config.systemPrompt != null) {
      apiMessages.add({'role': 'system', 'content': config.systemPrompt});
    }

    // Convert messages to xAI format
    for (final message in messages) {
      apiMessages.add(_convertMessage(message));
    }

    final body = <String, dynamic>{
      'model': config.model,
      'messages': apiMessages,
      'stream': stream,
    };

    // Add optional parameters
    if (config.maxTokens != null) body['max_tokens'] = config.maxTokens;
    if (config.temperature != null) body['temperature'] = config.temperature;
    if (config.topP != null) body['top_p'] = config.topP;
    if (config.topK != null) body['top_k'] = config.topK;

    // Add tools if provided (Note: XAI doesn't support tools yet, but keeping for compatibility)
    final effectiveTools = tools ?? config.tools;
    if (effectiveTools != null && effectiveTools.isNotEmpty) {
      body['tools'] = effectiveTools.map((t) => t.toJson()).toList();

      final effectiveToolChoice = config.toolChoice;
      if (effectiveToolChoice != null) {
        body['tool_choice'] = effectiveToolChoice.toJson();
      }
    }

    // Add structured output if configured
    if (config.jsonSchema != null) {
      body['response_format'] = {
        'type': 'json_schema',
        'json_schema': config.jsonSchema!.toJson(),
      };
    }

    // Add search parameters if configured
    if (config.searchParameters != null) {
      body['search_parameters'] = config.searchParameters!.toJson();
    }

    return body;
  }

  Map<String, dynamic> _convertMessage(ChatMessage message) {
    final result = <String, dynamic>{'role': message.role.name};

    switch (message.messageType) {
      case TextMessage():
        result['content'] = message.content;
        break;
      case ToolUseMessage(toolCalls: final toolCalls):
        result['tool_calls'] = toolCalls.map((tc) => tc.toJson()).toList();
        break;
      case ToolResultMessage():
        // Tool results are handled as separate messages in xAI
        // This should be handled at a higher level
        result['content'] = message.content;
        break;
      default:
        result['content'] = message.content;
    }

    return result;
  }

  ChatStreamEvent? _parseStreamEvent(Map<String, dynamic> json) {
    final choices = json['choices'] as List?;
    if (choices == null || choices.isEmpty) return null;

    final delta = choices.first['delta'] as Map<String, dynamic>?;
    if (delta == null) return null;

    final content = delta['content'] as String?;
    if (content != null) {
      return TextDeltaEvent(content);
    }

    // XAI doesn't support tool calls yet, so we don't parse them
    return null;
  }

  LLMError _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return HttpError('Request timeout: ${e.message}');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final data = e.response?.data;
        if (statusCode == 401) {
          return const AuthError('Invalid API key');
        } else if (statusCode == 429) {
          return const ProviderError('Rate limit exceeded');
        } else {
          return ProviderError('HTTP $statusCode: $data');
        }
      case DioExceptionType.cancel:
        return const GenericError('Request was cancelled');
      case DioExceptionType.connectionError:
        return HttpError('Connection error: ${e.message}');
      default:
        return HttpError('Network error: ${e.message}');
    }
  }

  /// Generate embeddings for the given texts using xAI's embedding API
  Future<List<List<double>>> embed(List<String> texts) async {
    if (config.apiKey.isEmpty) {
      throw const AuthError('Missing xAI API key');
    }

    try {
      final embeddingFormat = config.embeddingEncodingFormat ?? 'float';

      final requestBody = <String, dynamic>{
        'model': config.model,
        'input': texts,
        'encoding_format': embeddingFormat,
      };

      if (config.embeddingDimensions != null) {
        requestBody['dimensions'] = config.embeddingDimensions;
      }

      final response = await _dio.post('embeddings', data: requestBody);

      if (response.statusCode != 200) {
        throw ProviderError(
          'xAI API returned status ${response.statusCode}: ${response.data}',
        );
      }

      final embeddingResponse = XAIEmbeddingResponse.fromJson(
        response.data as Map<String, dynamic>,
      );

      return embeddingResponse.data.map((d) => d.embedding).toList();
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (e) {
      throw GenericError('Unexpected error: $e');
    }
  }

  // CompletionProvider methods
  @override
  Future<CompletionResponse> complete(CompletionRequest request) async {
    // Convert completion request to chat format
    final messages = [ChatMessage.user(request.prompt)];

    // Convert CompletionRequest parameters to ChatOptions
    final options = ChatOptions(
      maxTokens: request.maxTokens,
      temperature: request.temperature,
      topP: request.topP,
      topK: request.topK,
      stop: request.stop,
    );

    final response = await chat(messages, options: options);
    return CompletionResponse(text: response.text ?? '', usage: response.usage);
  }

  // LLMProvider methods
  @override
  List<Tool>? get tools => config.tools;
}
